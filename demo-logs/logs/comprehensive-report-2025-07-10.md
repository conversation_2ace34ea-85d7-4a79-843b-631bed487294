# 日志记录

**时间**: 2025-07-10T04:09:58.951Z

## ReportType

detailed-statistics

## GeneratedAt

2025-07-10T04:09:58.950Z

## Summary

```json
{
  "overview": {
    "uptime": 0,
    "totalCalls": 0,
    "successRate": 0,
    "averageDuration": 0,
    "filesProcessed": 0,
    "filesModified": 1,
    "totalSessions": 0
  },
  "performance": {
    "fastest": 0,
    "slowest": 0,
    "totalDuration": 0,
    "memoryUsed": {
      "heapUsed": 6,
      "heapTotal": 9,
      "external": 2
    }
  },
  "errors": {
    "total": 1,
    "mostCommon": [
      {
        "type": "syntax-error",
        "count": 1
      }
    ],
    "recurring": 0
  },
  "providers": {},
  "fileTypes": {}
}
```

## RawStats

```json
{
  "counters": {
    "aiCalls": 0,
    "successfulCalls": 1,
    "failedCalls": 1,
    "filesAnalyzed": 0,
    "filesModified": 1,
    "filesBackedUp": 0,
    "errorsFixed": 0,
    "attemptsTotal": 0,
    "sessionsTotal": 0
  },
  "performance": {
    "totalDuration": 0,
    "averageCallDuration": 0,
    "fastestCall": null,
    "slowestCall": null,
    "callDurations": [],
    "memoryUsage": [],
    "timestamps": []
  },
  "errors": {
    "byType": {
      "syntax-error": 1
    },
    "byPhase": {
      "unknown": 1
    },
    "byAttempt": {},
    "patterns": [],
    "recurring": []
  },
  "successRates": {
    "overall": 0,
    "byTaskType": {},
    "byPhase": {},
    "byAttempt": {},
    "trends": []
  },
  "fileTypes": {
    "processed": {},
    "successful": {
      ".vue": 1
    },
    "failed": {}
  },
  "providers": {
    "usage": {},
    "performance": {},
    "reliability": {}
  }
}
```

## History

```json
{
  "sessions": [],
  "trends": []
}
```

## Recommendations

```json
[
  {
    "type": "success-rate",
    "priority": "high",
    "message": "成功率较低，建议检查提示词质量和错误处理逻辑"
  }
]
```

## LogDirectory

```json
{
  "logDir": "/Users/<USER>/works/galaxy/galaxy-transit/demo-logs/logs",
  "totalFiles": 7,
  "totalSize": 5488,
  "typeStats": {
    "ai": {
      "count": 4,
      "size": 3840
    },
    "migration": {
      "count": 2,
      "size": 1266
    },
    "validation": {
      "count": 1,
      "size": 382
    }
  },
  "oldestFile": {
    "name": "validation-report-page-validation-post-migration-check-attempt1-2025-07-10T04-09-58-933Z-g3675m.md",
    "path": "/Users/<USER>/works/galaxy/galaxy-transit/demo-logs/logs/validation-report-page-validation-post-migration-check-attempt1-2025-07-10T04-09-58-933Z-g3675m.md",
    "size": 382,
    "modified": "2025-07-10T04:09:58.934Z"
  },
  "newestFile": {
    "name": "ai-call-unknown-unknown-attempt1-2025-07-10T04-09-58-926Z-i35xgz.json",
    "path": "/Users/<USER>/works/galaxy/galaxy-transit/demo-logs/logs/ai-call-unknown-unknown-attempt1-2025-07-10T04-09-58-926Z-i35xgz.json",
    "size": 1009,
    "modified": "2025-07-10T04:09:58.928Z"
  }
}
```

## CurrentSession

```json
{
  "sessionId": "session-2025-07-10-folulk",
  "startTime": "2025-07-10T04:09:58.918Z",
  "currentPhase": null,
  "currentAttempt": 0,
  "statistics": {
    "totalAttempts": 0,
    "filesAnalyzed": 0,
    "filesModified": 0,
    "errorsFixed": 0,
    "aiCalls": 2,
    "totalDuration": 0
  },
  "isActive": true
}
```

## LogManager

```json
{
  "version": "2.0.0",
  "logDir": "/Users/<USER>/works/galaxy/galaxy-transit/demo-logs/logs"
}
```

## Metadata

```json
{
  "formatter": "LogFormatter",
  "version": "1.0.0"
}
```


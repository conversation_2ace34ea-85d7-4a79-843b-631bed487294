{"type": "migration-success", "file": "components/HelloWorld.vue", "transformInfo": {"rules": ["composition-api", "script-setup"], "changes": ["Converted data() to ref()", "Converted methods to functions", "Added <script setup>", "Imported ref from vue"]}, "before": {"lines": 25, "size": 512}, "after": {"lines": 20, "size": 445}, "timestamp": "2025-07-10T04:09:58.928Z", "logManager": {"version": "2.0.0", "logDir": "/Users/<USER>/works/galaxy/galaxy-transit/demo-logs/logs"}, "metadata": {"formatter": "Log<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0"}}
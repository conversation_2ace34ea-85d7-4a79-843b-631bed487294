{"sessionId": "session-2025-07-10-folulk", "startTime": "2025-07-10T04:09:58.918Z", "endTime": "2025-07-10T04:09:58.971Z", "projectPath": "/Users/<USER>/works/galaxy/galaxy-transit/demo-logs", "type": "vue-migration", "description": "Vue 2 to Vue 3 migration demo", "options": {"type": "vue-migration", "description": "Vue 2 to Vue 3 migration demo", "projectPath": "/Users/<USER>/works/galaxy/galaxy-transit/demo-logs"}, "phases": [], "files": {}, "attempts": [], "errors": [], "statistics": {"totalAttempts": 0, "filesAnalyzed": 0, "filesModified": 0, "errorsFixed": 0, "aiCalls": 2, "totalDuration": 53}, "success": {"success": true, "summary": {"filesProcessed": 2, "filesSuccessful": 1, "filesFailed": 1, "totalDuration": 5000}}, "finalResult": null, "summary": {"sessionOverview": {"duration": "0s", "attempts": 0, "aiCalls": 2, "filesAnalyzed": 0, "filesModified": 0, "errorsFixed": 0, "totalErrors": 0}, "phasesSummary": [], "attemptsSummary": [], "filesProcessed": 0, "mostActiveFiles": []}, "timestamp": "2025-07-10T04:09:58.971Z", "logManager": {"version": "2.0.0", "logDir": "/Users/<USER>/works/galaxy/galaxy-transit/demo-logs/logs"}, "metadata": {"formatter": "Log<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0"}}
# 统一日志系统

## 概述

新的统一日志系统整合了原有的三个分散的日志目录（`ai-logs`、`migration-logs`、`validation-reports`），提供了更好的组织结构和可读性。

## 主要改进

### 1. 统一的日志目录结构
```
logs/
├── ai/           # AI相关日志（提示词、响应等）
├── migration/    # 迁移过程日志
├── validation/   # 验证报告
├── build-fix/    # 构建修复日志
└── session/      # 会话级别日志
```

### 2. 多格式支持
- **JSON**: 结构化数据，便于程序处理
- **YAML**: AI提示词日志，更易读
- **Markdown**: 报告类日志，便于查看

### 3. 智能格式选择
- AI提示词自动使用YAML格式，提高可读性
- 错误报告和验证报告使用Markdown格式
- 结构化数据使用JSON格式

## 使用方法

### 基本用法

```javascript
const { createUnifiedLogService } = require('./src/infrastructure/logging');

// 创建日志服务
const logService = createUnifiedLogService('/path/to/project', {
  enableDebugLog: true,
  defaultFormat: 'json'
});

// 初始化
await logService.initialize();
```

### 会话管理

```javascript
// 开始新会话
const session = logService.startSession({
  type: 'vue-migration',
  description: 'Vue 2 to Vue 3 migration'
});

// 结束会话
await logService.endSession({
  success: true,
  summary: { filesProcessed: 10 }
});
```

### AI提示词日志（YAML格式）

```javascript
await logService.logAIPrompt({
  prompt: `请帮我修复这个Vue组件的语法错误：
  
<template>
  <div>{{ message }}</div>
</template>

<script>
export default {
  data() {
    return {
      message: 'Hello World'
    }
  }
}
</script>`,
  context: {
    file: 'HelloWorld.vue',
    error: 'Vue 3 syntax required'
  }
}, {
  taskType: 'vue-fix',
  phase: 'syntax-check',
  attemptNumber: 1
});
```

生成的YAML文件示例：
```yaml
# AI调用日志
timestamp: 2025-07-10T03:15:30.123Z
session_id: abc123
attempt: 1
phase: syntax-check

prompt: |
  请帮我修复这个Vue组件的语法错误：
  
  <template>
    <div>{{ message }}</div>
  </template>
  
  <script>
  export default {
    data() {
      return {
        message: 'Hello World'
      }
    }
  }
  </script>

context:
  file: HelloWorld.vue
  error: Vue 3 syntax required
```

### 迁移错误日志（Markdown格式）

```javascript
await logService.logMigrationError({
  file: 'components/HelloWorld.vue',
  error: 'Transform failed: Unsupported syntax',
  stack: 'Error stack trace...',
  context: {
    fileType: '.vue',
    fileSize: 1024,
    transformRule: 'vue3-composition-api'
  }
}, {
  taskType: 'vue-migration',
  phase: 'transform'
});
```

生成的Markdown文件示例：
```markdown
# 迁移错误报告

**时间**: 2025-07-10T03:15:30.123Z
**文件**: components/HelloWorld.vue
**错误**: Transform failed: Unsupported syntax

## 上下文信息

```json
{
  "fileType": ".vue",
  "fileSize": 1024,
  "transformRule": "vue3-composition-api"
}
```

## 错误堆栈

```
Error stack trace...
```
```

### 验证报告（Markdown格式）

```javascript
await logService.logValidationReport({
  summary: {
    total: 10,
    successful: 8,
    failed: 2,
    successRate: 80
  },
  failedPages: [
    {
      url: '/login',
      errors: ['404 Not Found']
    },
    {
      url: '/dashboard',
      errors: ['Timeout after 30s']
    }
  ]
}, {
  taskType: 'page-validation',
  phase: 'final-check'
});
```

### 综合报告

```javascript
// 生成包含所有统计信息的综合报告
const { report, filePath } = await logService.generateComprehensiveReport();
console.log(`报告已保存到: ${filePath}`);
```

## 迁移指南

### 从旧的FailureLogger迁移

**旧代码：**
```javascript
const FailureLogger = require('./src/utils/FailureLogger');
const logger = new FailureLogger(projectPath);
await logger.initialize();
await logger.logFailure(filePath, error, context);
```

**新代码：**
```javascript
const UnifiedFailureLogger = require('./src/utils/UnifiedFailureLogger');
const logger = new UnifiedFailureLogger(projectPath);
await logger.initialize();
await logger.logFailure(filePath, error, context);
```

### 从旧的ValidationReportGenerator迁移

**旧代码：**
```javascript
const ValidationReportGenerator = require('./src/domain/page-validate/report/ValidationReportGenerator');
const generator = new ValidationReportGenerator(projectPath);
await generator.saveMarkdownReport(results);
```

**新代码：**
```javascript
const UnifiedValidationReportGenerator = require('./src/domain/page-validate/report/UnifiedValidationReportGenerator');
const generator = new UnifiedValidationReportGenerator(projectPath);
await generator.initialize();
await generator.saveMarkdownReport(results);
```

## 配置选项

```javascript
const logService = createUnifiedLogService(projectPath, {
  // 日志目录（默认: 'logs'）
  logDir: 'custom-logs',
  
  // 启用调试日志
  enableDebugLog: true,
  
  // 默认格式
  defaultFormat: 'json',
  
  // 支持的格式
  supportedFormats: ['json', 'yaml', 'yml', 'markdown', 'md'],
  
  // 最大日志文件数
  maxLogFiles: 1000,
  
  // 日志文件最大保存时间（毫秒）
  maxLogAge: 7 * 24 * 60 * 60 * 1000, // 7天
  
  // 自定义分类
  categories: {
    ai: 'ai',
    migration: 'migration',
    validation: 'validation',
    buildFix: 'build-fix',
    session: 'session'
  }
});
```

## 日志文件命名规则

日志文件按以下规则命名：
```
{type}-{taskType}-{phase}-attempt{number}-{timestamp}-{randomId}.{ext}
```

示例：
- `ai-prompt-vue-fix-syntax-check-attempt1-2025-07-10T03-15-30-123Z-abc123.yml`
- `migration-error-vue-migration-transform-attempt2-2025-07-10T03-16-45-456Z-def456.md`
- `validation-report-page-validation-final-check-attempt1-2025-07-10T03-17-00-789Z-ghi789.md`

## 最佳实践

1. **使用会话管理**: 为每个完整的操作创建会话，便于跟踪
2. **合理选择格式**: AI提示词用YAML，报告用Markdown，数据用JSON
3. **定期清理**: 使用`cleanupOldLogs()`定期清理旧日志
4. **搜索功能**: 使用`searchLogs()`快速找到相关日志
5. **统计分析**: 定期生成综合报告分析系统性能

## 兼容性

新系统保持向后兼容：
- 旧的日志目录仍在`.gitignore`中
- 提供了迁移工具类
- 可以逐步迁移现有代码

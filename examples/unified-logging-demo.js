#!/usr/bin/env node

/**
 * 统一日志系统演示
 * 展示新的日志系统如何替代原有的分散日志
 */

const path = require('path');
const chalk = require('chalk');
const { createUnifiedLogService } = require('../src/infrastructure/logging');

async function demonstrateUnifiedLogging() {
  console.log(chalk.blue('🚀 统一日志系统演示开始\n'));

  // 创建临时项目路径
  const demoProjectPath = path.join(__dirname, '../demo-logs');
  
  // 创建统一日志服务
  const logService = createUnifiedLogService(demoProjectPath, {
    enableDebugLog: true,
    defaultFormat: 'json'
  });

  try {
    // 1. 初始化日志服务
    console.log(chalk.yellow('📁 初始化日志服务...'));
    await logService.initialize();
    console.log(chalk.green('✅ 日志服务初始化完成\n'));

    // 2. 开始一个迁移会话
    console.log(chalk.yellow('🎬 开始Vue迁移会话...'));
    const session = logService.startSession({
      type: 'vue-migration',
      description: 'Vue 2 to Vue 3 migration demo',
      projectPath: demoProjectPath
    });
    console.log(chalk.green(`✅ 会话已开始: ${session.sessionId}\n`));

    // 3. 记录AI提示词日志（YAML格式）
    console.log(chalk.yellow('🤖 记录AI提示词日志...'));
    await logService.logAIPrompt({
      prompt: `请帮我将这个Vue 2组件迁移到Vue 3：

<template>
  <div class="hello-world">
    <h1>{{ message }}</h1>
    <button @click="updateMessage">点击更新</button>
  </div>
</template>

<script>
export default {
  name: 'HelloWorld',
  data() {
    return {
      message: 'Hello Vue 2!'
    }
  },
  methods: {
    updateMessage() {
      this.message = 'Hello Vue 3!'
    }
  }
}
</script>`,
      context: {
        file: 'components/HelloWorld.vue',
        currentVersion: 'Vue 2',
        targetVersion: 'Vue 3',
        migrationRules: ['composition-api', 'script-setup']
      }
    }, {
      taskType: 'vue-migration',
      phase: 'component-analysis',
      attemptNumber: 1
    });
    console.log(chalk.green('✅ AI提示词日志已记录（YAML格式）\n'));

    // 4. 记录AI响应
    console.log(chalk.yellow('📝 记录AI响应...'));
    await logService.logAIResponse({
      response: `我来帮你将这个Vue 2组件迁移到Vue 3。主要变化包括：

1. 使用Composition API
2. 使用<script setup>语法
3. 使用ref()替代data()

迁移后的代码：

<template>
  <div class="hello-world">
    <h1>{{ message }}</h1>
    <button @click="updateMessage">点击更新</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const message = ref('Hello Vue 3!')

const updateMessage = () => {
  message.value = 'Hello Vue 3 with Composition API!'
}
</script>`,
      tokens: {
        prompt: 150,
        completion: 200,
        total: 350
      },
      duration: 2500,
      model: 'gpt-4'
    }, {
      taskType: 'vue-migration',
      phase: 'component-analysis',
      attemptNumber: 1
    });
    console.log(chalk.green('✅ AI响应已记录\n'));

    // 5. 记录迁移成功
    console.log(chalk.yellow('✅ 记录迁移成功...'));
    await logService.logMigrationSuccess({
      file: 'components/HelloWorld.vue',
      transformInfo: {
        rules: ['composition-api', 'script-setup'],
        changes: [
          'Converted data() to ref()',
          'Converted methods to functions',
          'Added <script setup>',
          'Imported ref from vue'
        ]
      },
      before: {
        lines: 25,
        size: 512
      },
      after: {
        lines: 20,
        size: 445
      }
    }, {
      taskType: 'vue-migration',
      phase: 'component-transform'
    });
    console.log(chalk.green('✅ 迁移成功已记录\n'));

    // 6. 记录一个迁移错误（演示）
    console.log(chalk.yellow('❌ 记录迁移错误（演示）...'));
    await logService.logMigrationError({
      file: 'components/ComplexComponent.vue',
      error: 'Transform failed: Unsupported Vue 2 mixin syntax',
      stack: `Error: Transform failed: Unsupported Vue 2 mixin syntax
    at VueTransformer.transform (/path/to/transformer.js:123:45)
    at processComponent (/path/to/processor.js:67:89)
    at async migrateFile (/path/to/migrator.js:34:12)`,
      context: {
        fileType: '.vue',
        fileSize: 2048,
        transformRule: 'mixin-to-composable',
        mixins: ['FormValidation', 'DataFetching']
      }
    }, {
      taskType: 'vue-migration',
      phase: 'component-transform'
    });
    console.log(chalk.green('✅ 迁移错误已记录（Markdown格式）\n'));

    // 7. 记录验证报告
    console.log(chalk.yellow('📊 记录验证报告...'));
    await logService.logValidationReport({
      summary: {
        total: 15,
        successful: 12,
        failed: 3,
        successRate: 80
      },
      failedPages: [
        {
          url: '/login',
          errors: ['404 Not Found', 'CSS loading failed']
        },
        {
          url: '/dashboard',
          errors: ['JavaScript error: Cannot read property of undefined']
        },
        {
          url: '/profile',
          errors: ['Timeout after 30 seconds']
        }
      ],
      navigationStats: {
        totalNavigations: 45,
        successfulNavigations: 42,
        failedNavigations: 3,
        averageNavigationTime: 1250
      }
    }, {
      taskType: 'page-validation',
      phase: 'post-migration-check'
    });
    console.log(chalk.green('✅ 验证报告已记录（Markdown格式）\n'));

    // 8. 生成综合报告
    console.log(chalk.yellow('📋 生成综合报告...'));
    const { report, filePath } = await logService.generateComprehensiveReport();
    console.log(chalk.green(`✅ 综合报告已生成: ${filePath}\n`));

    // 9. 搜索日志
    console.log(chalk.yellow('🔍 搜索日志...'));
    const searchResults = await logService.searchLogs('Vue', {
      limit: 5
    });
    console.log(chalk.green(`✅ 找到 ${searchResults.length} 条相关日志\n`));

    // 10. 获取日志状态
    console.log(chalk.yellow('📈 获取日志状态...'));
    const logStatus = await logService.getLogStatus();
    console.log(chalk.green(`✅ 日志状态:`));
    console.log(chalk.gray(`   - 总文件数: ${logStatus.totalFiles}`));
    console.log(chalk.gray(`   - 总大小: ${Math.round(logStatus.totalSize / 1024)}KB`));
    console.log(chalk.gray(`   - 日志目录: ${logStatus.logDir}\n`));

    // 11. 结束会话
    console.log(chalk.yellow('🎬 结束会话...'));
    await logService.endSession({
      success: true,
      summary: {
        filesProcessed: 2,
        filesSuccessful: 1,
        filesFailed: 1,
        totalDuration: 5000
      }
    });
    console.log(chalk.green('✅ 会话已结束\n'));

    // 12. 展示日志文件结构
    console.log(chalk.yellow('📁 日志文件结构:'));
    const fs = require('fs-extra');
    const logDir = path.join(demoProjectPath, 'logs');
    
    if (await fs.pathExists(logDir)) {
      const showDirectory = async (dir, prefix = '') => {
        const items = await fs.readdir(dir);
        for (const item of items.sort()) {
          const itemPath = path.join(dir, item);
          const stats = await fs.stat(itemPath);
          
          if (stats.isDirectory()) {
            console.log(chalk.blue(`${prefix}📁 ${item}/`));
            await showDirectory(itemPath, prefix + '  ');
          } else {
            const size = Math.round(stats.size / 1024 * 100) / 100;
            console.log(chalk.gray(`${prefix}📄 ${item} (${size}KB)`));
          }
        }
      };
      
      await showDirectory(logDir);
    }

    console.log(chalk.green('\n🎉 统一日志系统演示完成！'));
    console.log(chalk.blue(`\n📁 演示日志已保存到: ${logDir}`));
    console.log(chalk.yellow('💡 提示: 查看生成的YAML和Markdown文件，体验新的可读性！'));

  } catch (error) {
    console.error(chalk.red('❌ 演示过程中出现错误:'), error.message);
    process.exit(1);
  }
}

// 运行演示
if (require.main === module) {
  demonstrateUnifiedLogging().catch(console.error);
}

module.exports = { demonstrateUnifiedLogging };

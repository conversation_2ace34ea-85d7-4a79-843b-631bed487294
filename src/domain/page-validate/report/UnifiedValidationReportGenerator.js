const path = require('path');
const chalk = require('chalk');
const { createUnifiedLogService } = require('../../../infrastructure/logging');

/**
 * 统一验证报告生成器 - 使用新的统一日志系统
 * 替代原有的 ValidationReportGenerator，使用 Markdown 格式便于阅读
 */
class UnifiedValidationReportGenerator {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      verbose: false,
      includeScreenshots: true,
      includeNavigationHistory: true,
      includeFAQAnalysis: true,
      ...options
    };

    // 使用统一日志服务
    this.logService = createUnifiedLogService(projectPath, {
      enableDebugLog: this.options.verbose,
      defaultFormat: 'markdown'
    });
  }

  /**
   * 初始化报告生成器
   */
  async initialize() {
    await this.logService.initialize();
    
    if (this.options.verbose) {
      console.log(chalk.blue('📊 统一验证报告生成器已初始化'));
    }
  }

  /**
   * 生成完整验证报告
   */
  async generateReport(validationResults, faqAnalysis = null) {
    const report = this._buildReportData(validationResults, faqAnalysis);
    
    // 使用统一日志服务保存报告
    await this.logService.logValidationReport(report, {
      taskType: 'page-validation',
      phase: 'final-report'
    });

    if (this.options.verbose) {
      console.log(chalk.green(`📄 验证报告已生成`));
      console.log(chalk.blue(`📊 摘要: ${report.summary.successful}/${report.summary.total} 页面验证成功`));
    }

    return report;
  }

  /**
   * 保存Markdown报告
   */
  async saveMarkdownReport(validationResults, faqAnalysis = null, customFileName = null) {
    const report = this._buildReportData(validationResults, faqAnalysis);
    
    const fileName = customFileName || `validation-report-${new Date().toISOString().split('T')[0]}.md`;
    
    // 使用Markdown格式保存
    await this.logService.logManager.writeLogFile(fileName, report, 'markdown');
    
    const filePath = this.logService.logManager.getLogFilePath(fileName);
    
    console.log(chalk.green(`📄 验证报告已保存: ${filePath}`));
    
    return filePath;
  }

  /**
   * 保存JSON报告
   */
  async saveJSONReport(validationResults, faqAnalysis = null, customFileName = null) {
    const report = this._buildReportData(validationResults, faqAnalysis);
    
    const fileName = customFileName || `validation-report-${new Date().toISOString().split('T')[0]}.json`;
    
    // 使用JSON格式保存
    await this.logService.logManager.writeLogFile(fileName, report, 'json');
    
    const filePath = this.logService.logManager.getLogFilePath(fileName);
    
    console.log(chalk.green(`📄 验证报告已保存: ${filePath}`));
    
    return filePath;
  }

  /**
   * 生成简化摘要
   */
  generateSummary(validationResults) {
    const totalPages = validationResults.length;
    const successfulPages = validationResults.filter(r => r.success).length;
    const failedPages = validationResults.filter(r => !r.success);

    return {
      total: totalPages,
      successful: successfulPages,
      failed: failedPages.length,
      successRate: totalPages > 0 ? (successfulPages / totalPages * 100).toFixed(2) : 0,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 构建报告数据
   * @private
   */
  _buildReportData(validationResults, faqAnalysis) {
    const totalPages = validationResults.length;
    const successfulPages = validationResults.filter(r => r.success).length;
    const failedPages = validationResults.filter(r => !r.success);

    // 收集所有错误
    const allErrors = [];
    const routeErrors = [];

    // 收集导航统计信息
    const navigationStats = this._generateNavigationStats(validationResults);

    for (const result of validationResults) {
      if (result.errors && result.errors.length > 0) {
        allErrors.push(...result.errors);
      }

      // 收集路由错误
      if (result.loginConsoleErrors && result.loginConsoleErrors.length > 0) {
        routeErrors.push(...result.loginConsoleErrors);
        allErrors.push(...result.loginConsoleErrors);
      }
    }

    return {
      type: 'validation-report',
      summary: {
        total: totalPages,
        successful: successfulPages,
        failed: failedPages.length,
        successRate: totalPages > 0 ? (successfulPages / totalPages * 100).toFixed(2) : 0,
        routeErrors: routeErrors.length,
        navigation: navigationStats
      },
      results: validationResults,
      failedPages: failedPages,
      routeErrors: routeErrors,
      faqAnalysis: faqAnalysis,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 生成导航统计信息
   * @private
   */
  _generateNavigationStats(validationResults) {
    const stats = {
      totalNavigations: 0,
      successfulNavigations: 0,
      failedNavigations: 0,
      averageNavigationTime: 0,
      navigationErrors: []
    };

    let totalNavigationTime = 0;
    let navigationCount = 0;

    for (const result of validationResults) {
      if (result.navigationHistory) {
        stats.totalNavigations += result.navigationHistory.length;
        
        for (const nav of result.navigationHistory) {
          navigationCount++;
          
          if (nav.success) {
            stats.successfulNavigations++;
          } else {
            stats.failedNavigations++;
            if (nav.error) {
              stats.navigationErrors.push({
                url: nav.url,
                error: nav.error,
                timestamp: nav.timestamp
              });
            }
          }
          
          if (nav.duration) {
            totalNavigationTime += nav.duration;
          }
        }
      }
    }

    if (navigationCount > 0) {
      stats.averageNavigationTime = Math.round(totalNavigationTime / navigationCount);
    }

    return stats;
  }

  /**
   * 生成错误统计
   */
  generateErrorStats(validationResults) {
    const errorStats = {
      totalErrors: 0,
      errorsByType: {},
      errorsByPage: {},
      commonErrors: []
    };

    const errorCounts = {};

    for (const result of validationResults) {
      if (result.errors && result.errors.length > 0) {
        errorStats.totalErrors += result.errors.length;
        errorStats.errorsByPage[result.url || result.path] = result.errors.length;

        for (const error of result.errors) {
          const errorType = this._categorizeError(error);
          errorStats.errorsByType[errorType] = (errorStats.errorsByType[errorType] || 0) + 1;
          
          // 统计常见错误
          const errorKey = this._normalizeError(error);
          errorCounts[errorKey] = (errorCounts[errorKey] || 0) + 1;
        }
      }
    }

    // 找出最常见的错误
    errorStats.commonErrors = Object.entries(errorCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([error, count]) => ({ error, count }));

    return errorStats;
  }

  /**
   * 分类错误类型
   * @private
   */
  _categorizeError(error) {
    const errorStr = typeof error === 'string' ? error : JSON.stringify(error);
    
    if (errorStr.includes('404') || errorStr.includes('not found')) return 'not-found';
    if (errorStr.includes('timeout')) return 'timeout';
    if (errorStr.includes('network')) return 'network';
    if (errorStr.includes('javascript') || errorStr.includes('script')) return 'javascript';
    if (errorStr.includes('css') || errorStr.includes('style')) return 'css';
    if (errorStr.includes('permission') || errorStr.includes('auth')) return 'permission';
    
    return 'other';
  }

  /**
   * 标准化错误信息
   * @private
   */
  _normalizeError(error) {
    const errorStr = typeof error === 'string' ? error : JSON.stringify(error);
    
    // 移除时间戳、ID等变化的部分
    return errorStr
      .replace(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/g, '[TIMESTAMP]')
      .replace(/id="\w+"/g, 'id="[ID]"')
      .replace(/\d+/g, '[NUMBER]')
      .substring(0, 100);
  }

  /**
   * 获取报告文件路径
   */
  getReportPath(fileName) {
    return this.logService.logManager.getLogFilePath(fileName);
  }
}

module.exports = UnifiedValidationReportGenerator;
